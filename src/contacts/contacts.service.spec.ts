import { Test, TestingModule } from "@nestjs/testing";
import { ContactsService } from "./contacts.service";
import { getModelToken } from "@nestjs/mongoose";
import { BadRequestException, InternalServerErrorException, NotFoundException } from "@nestjs/common";

describe("ContactsService", () => {
    let service: ContactsService;

    // Mocks
    const mockContactModel = jest.fn() as any;
    mockContactModel.mockImplementation(() => ({
        save: jest.fn(),
        aggregate: jest.fn(),
        findOne: jest.fn(),
        findOneAndUpdate: jest.fn(),
        updateOne: jest.fn(),
        updateMany: jest.fn(),
        findOneAndDelete: jest.fn(),
    }));
    mockContactModel.save = jest.fn();
    mockContactModel.aggregate = jest.fn();
    mockContactModel.findOne = jest.fn();
    mockContactModel.findOneAndUpdate = jest.fn();
    mockContactModel.updateOne = jest.fn();
    mockContactModel.updateMany = jest.fn();
    mockContactModel.findOneAndDelete = jest.fn();

    const mockLeadModel = jest.fn() as any;
    mockLeadModel.mockImplementation(() => ({
        save: jest.fn(),
    }));
    mockLeadModel.save = jest.fn();

    const mockActivityLogModel = jest.fn() as any;
    mockActivityLogModel.mockImplementation(() => ({
        save: jest.fn(),
    }));
    mockActivityLogModel.save = jest.fn();
    const mockCrmStageModel = {
        findOne: jest.fn(),
    };
    const mockOpportunityModel = {
        aggregate: jest.fn(),
    };
    const mockSession = {
        startTransaction: jest.fn(),
        commitTransaction: jest.fn(),
        abortTransaction: jest.fn(),
        endSession: jest.fn(),
    };
    const mockConnection = {
        startSession: jest.fn(() => mockSession),
    };

    beforeEach(async () => {
        jest.clearAllMocks();
        const module: TestingModule = await Test.createTestingModule({
            providers: [
                ContactsService,
                { provide: getModelToken("Contact"), useValue: mockContactModel },
                { provide: getModelToken("Lead"), useValue: mockLeadModel },
                { provide: getModelToken("ActivityLog"), useValue: mockActivityLogModel },
                { provide: getModelToken("CrmStage"), useValue: mockCrmStageModel },
                { provide: getModelToken("Client"), useValue: {} },
                { provide: getModelToken("Opportunity"), useValue: mockOpportunityModel },
                { provide: getModelToken("Project"), useValue: {} },
                { provide: getModelToken("Price"), useValue: {} },
                { provide: getModelToken("OpportunityActivity"), useValue: {} },
                { provide: getModelToken("Referrers"), useValue: {} },
                { provide: getModelToken("CrmStage"), useValue: mockCrmStageModel },
                { provide: "Connection", useValue: mockConnection },
                { provide: "DatabaseConnection", useValue: mockConnection },
            ],
        }).compile();

        service = module.get<ContactsService>(ContactsService);
    });

    it("should be defined", () => {
        expect(service).toBeDefined();
    });

    describe("create", () => {
        const mockUser = { companyId: "company1", memberId: "member1" };
        const mockStageData = { _id: "stage1", defaultCsrId: "defaultCsr1" };

        beforeEach(() => {
            jest.clearAllMocks();
            mockCrmStageModel.findOne.mockResolvedValue(mockStageData);
            mockLeadModel.save.mockResolvedValue({});
            mockContactModel.save.mockResolvedValue({});
            mockActivityLogModel.save.mockResolvedValue({});
            mockSession.commitTransaction.mockResolvedValue({});
            mockSession.endSession.mockResolvedValue({});
        });

        describe("when creating a LEAD contact", () => {
            let leadInstance: any;
            let contactInstance: any;
            let activityLogInstance: any;
            beforeEach(() => {
                (mockLeadModel as jest.Mock).mockImplementation(() => {
                    leadInstance = {
                        save: jest.fn().mockResolvedValue({}),
                    };
                    return leadInstance;
                });
                (mockContactModel as jest.Mock).mockImplementation(() => {
                    contactInstance = {
                        save: jest.fn().mockResolvedValue({}),
                    };
                    return contactInstance;
                });
                (mockActivityLogModel as jest.Mock).mockImplementation(() => {
                    activityLogInstance = {
                        save: jest.fn().mockResolvedValue({}),
                    };
                    return activityLogInstance;
                });
            });

            it("should create a lead contact with all required fields and commit transaction", async () => {
                // Arrange
                const dto: any = {
                    type: "lead",
                    fullName: "John Doe",
                    firstName: "John",
                    lastName: "Doe",
                    phone: "1234567890",
                    email: "<EMAIL>",
                    status: "active",
                    csrId: "customCsr1",
                };

                // Act
                const result = await service.create(dto, mockUser);

                // Assert
                expect(mockConnection.startSession).toHaveBeenCalled();
                expect(mockSession.startTransaction).toHaveBeenCalled();
                expect(mockCrmStageModel.findOne).toHaveBeenCalledWith(
                    {
                        companyId: mockUser.companyId,
                        deleted: false,
                        stageGroup: "leads",
                        sequence: 1,
                    },
                    { _id: 1, defaultCsrId: 1 },
                );
                expect(leadInstance.save).toHaveBeenCalledWith({ session: mockSession });
                expect(contactInstance.save).toHaveBeenCalledWith({ session: mockSession });
                expect(activityLogInstance.save).toHaveBeenCalledWith({ session: mockSession });
                expect(mockSession.commitTransaction).toHaveBeenCalled();
                expect(mockSession.endSession).toHaveBeenCalled();
                expect(result).toHaveProperty("data");
                expect(result.data).toHaveProperty("id");
                expect(result.data).toHaveProperty("contact");
                expect(result.data).toHaveProperty("message", "Contact created successfully");
            });

            it("should create a lead contact with default CSR ID when not provided", async () => {
                // Arrange
                const dto: any = {
                    type: "lead",
                    fullName: "Jane Smith",
                    firstName: "Jane",
                    lastName: "Smith",
                };

                // Act
                const result = await service.create(dto, mockUser);

                // Assert
                expect(mockCrmStageModel.findOne).toHaveBeenCalled();
                expect(leadInstance.save).toHaveBeenCalled();
                expect(result).toHaveProperty("data");
            });

            it("should set newLeadDate to current date when not provided", async () => {
                // Arrange
                const dto: any = {
                    type: "lead",
                    fullName: "Bob Wilson",
                    firstName: "Bob",
                    lastName: "Wilson",
                };

                // Act
                await service.create(dto, mockUser);

                // Assert
                const leadCtorArg = (mockLeadModel as jest.Mock).mock.calls[0][0];
                expect(leadCtorArg).toHaveProperty("newLeadDate");
                const now = new Date();
                const leadDate = new Date(leadCtorArg.newLeadDate);
                expect(leadDate.getTime()).toBeGreaterThan(now.getTime() - 60000);
                expect(leadDate.getTime()).toBeLessThan(now.getTime() + 60000);
            });

            it("should use provided newLeadDate when available", async () => {
                // Arrange
                const customDate = new Date("2023-01-15");
                const dto: any = {
                    type: "lead",
                    fullName: "Alice Brown",
                    firstName: "Alice",
                    lastName: "Brown",
                    newLeadDate: customDate,
                };

                // Act
                await service.create(dto, mockUser);

                // Assert
                const leadCtorArg = (mockLeadModel as jest.Mock).mock.calls[0][0];
                expect(leadCtorArg).toHaveProperty("newLeadDate");
                expect(leadCtorArg.newLeadDate).toEqual(customDate);
            });
        });

        describe("when creating a non-LEAD contact", () => {
            let contactInstance: any;
            let activityLogInstance: any;
            beforeEach(() => {
                (mockContactModel as jest.Mock).mockImplementation(() => {
                    contactInstance = {
                        save: jest.fn().mockResolvedValue({}),
                    };
                    return contactInstance;
                });
                (mockActivityLogModel as jest.Mock).mockImplementation(() => {
                    activityLogInstance = {
                        save: jest.fn().mockResolvedValue({}),
                    };
                    return activityLogInstance;
                });
            });

            it("should create a CLIENT contact without creating a lead", async () => {
                // Arrange
                const dto: any = {
                    type: "client",
                    fullName: "Client Name",
                    firstName: "Client",
                    lastName: "Name",
                    phone: "9876543210",
                    email: "<EMAIL>",
                };

                // Act
                const result = await service.create(dto, mockUser);

                // Assert
                expect(mockCrmStageModel.findOne).not.toHaveBeenCalled();
                expect(contactInstance.save).toHaveBeenCalledWith({ session: mockSession });
                expect(activityLogInstance.save).toHaveBeenCalledWith({ session: mockSession });
                expect(result).toHaveProperty("data");
            });

            it("should create a PROSPECT contact without creating a lead", async () => {
                // Arrange
                const dto: any = {
                    type: "prospect",
                    fullName: "Prospect Name",
                    firstName: "Prospect",
                    lastName: "Name",
                };

                // Act
                const result = await service.create(dto, mockUser);

                // Assert
                expect(mockCrmStageModel.findOne).not.toHaveBeenCalled();
                expect(contactInstance.save).toHaveBeenCalledWith({ session: mockSession });
                expect(result).toHaveProperty("data");
            });

            it("should create a VENDOR contact without creating a lead", async () => {
                // Arrange
                const dto: any = {
                    type: "vendor",
                    fullName: "Vendor Name",
                    firstName: "Vendor",
                    lastName: "Name",
                };

                // Act
                const result = await service.create(dto, mockUser);

                // Assert
                expect(mockCrmStageModel.findOne).not.toHaveBeenCalled();
                expect(contactInstance.save).toHaveBeenCalledWith({ session: mockSession });
                expect(result).toHaveProperty("data");
            });
        });

        describe("activity log creation", () => {
            let activityLogInstance: any;
            beforeEach(() => {
                (mockActivityLogModel as jest.Mock).mockImplementation(() => {
                    activityLogInstance = {
                        save: jest.fn().mockResolvedValue({}),
                    };
                    return activityLogInstance;
                });
            });

            it("should create activity log with 'created a New Contact' for non-lead contacts", async () => {
                // Arrange
                const dto: any = {
                    type: "client",
                    fullName: "Test Client",
                    firstName: "Test",
                    lastName: "Client",
                };

                // Act
                await service.create(dto, mockUser);

                // Assert
                const activityLogCtorArg = (mockActivityLogModel as jest.Mock).mock.calls[0][0];
                expect(activityLogCtorArg.activities[0].body).toBe("created a New Contact");
            });

            it("should create activity log with 'created a New Lead' for lead contacts", async () => {
                // Arrange
                const dto: any = {
                    type: "lead",
                    fullName: "Test Lead",
                    firstName: "Test",
                    lastName: "Lead",
                };
                // Patch lead and contact model for this test
                let leadInstance: any;
                let contactInstance: any;
                (mockLeadModel as jest.Mock).mockImplementation(() => {
                    leadInstance = {
                        save: jest.fn().mockResolvedValue({}),
                    };
                    return leadInstance;
                });
                (mockContactModel as jest.Mock).mockImplementation(() => {
                    contactInstance = {
                        save: jest.fn().mockResolvedValue({}),
                    };
                    return contactInstance;
                });

                // Act
                await service.create(dto, mockUser);

                // Assert
                const activityLogCtorArg = (mockActivityLogModel as jest.Mock).mock.calls[0][0];
                expect(activityLogCtorArg.activities[0].body).toBe("created a New Lead");
            });
        });

        describe("error handling", () => {
            beforeEach(() => {
                // Reset mock implementations before each error test
                (mockContactModel as jest.Mock).mockReset();
                (mockLeadModel as jest.Mock).mockReset();
                (mockActivityLogModel as jest.Mock).mockReset();
            });

            it("should abort transaction and throw InternalServerErrorException when contact save fails", async () => {
                // Arrange
                const dto: any = {
                    type: "client",
                    fullName: "Test Client",
                    firstName: "Test",
                    lastName: "Client",
                };
                (mockContactModel as jest.Mock).mockImplementation(() => ({
                    save: jest.fn().mockRejectedValue(new Error("Database connection failed")),
                }));

                // Act & Assert
                await expect(service.create(dto, mockUser)).rejects.toThrow(InternalServerErrorException);
                expect(mockSession.abortTransaction).toHaveBeenCalled();
                expect(mockSession.endSession).toHaveBeenCalled();
            });

            it("should abort transaction and throw InternalServerErrorException when lead save fails", async () => {
                // Arrange
                const dto: any = {
                    type: "lead",
                    fullName: "Test Lead",
                    firstName: "Test",
                    lastName: "Lead",
                };
                (mockLeadModel as jest.Mock).mockImplementation(() => ({
                    save: jest.fn().mockRejectedValue(new Error("Lead creation failed")),
                }));

                // Act & Assert
                await expect(service.create(dto, mockUser)).rejects.toThrow(InternalServerErrorException);
                expect(mockSession.abortTransaction).toHaveBeenCalled();
                expect(mockSession.endSession).toHaveBeenCalled();
            });

            it("should abort transaction and throw InternalServerErrorException when activity log save fails", async () => {
                // Arrange
                const dto: any = {
                    type: "client",
                    fullName: "Test Client",
                    firstName: "Test",
                    lastName: "Client",
                };
                (mockActivityLogModel as jest.Mock).mockImplementation(() => ({
                    save: jest.fn().mockRejectedValue(new Error("Activity log creation failed")),
                }));

                // Act & Assert
                await expect(service.create(dto, mockUser)).rejects.toThrow(InternalServerErrorException);
                expect(mockSession.abortTransaction).toHaveBeenCalled();
                expect(mockSession.endSession).toHaveBeenCalled();
            });

            it("should abort transaction and throw InternalServerErrorException when CRM stage lookup fails", async () => {
                // Arrange
                const dto: any = {
                    type: "lead",
                    fullName: "Test Lead",
                    firstName: "Test",
                    lastName: "Lead",
                };
                mockCrmStageModel.findOne.mockRejectedValue(new Error("CRM stage not found"));

                // Act & Assert
                await expect(service.create(dto, mockUser)).rejects.toThrow(InternalServerErrorException);
                expect(mockSession.abortTransaction).toHaveBeenCalled();
                expect(mockSession.endSession).toHaveBeenCalled();
            });

            it("should re-throw HttpException when it occurs", async () => {
                // Arrange
                const dto: any = {
                    type: "client",
                    fullName: "Test Client",
                    firstName: "Test",
                    lastName: "Client",
                };
                (mockContactModel as jest.Mock).mockImplementation(() => ({
                    save: jest.fn().mockRejectedValue(new BadRequestException("Validation failed")),
                }));

                // Act & Assert
                await expect(service.create(dto, mockUser)).rejects.toThrow(BadRequestException);
                expect(mockSession.abortTransaction).toHaveBeenCalled();
                expect(mockSession.endSession).toHaveBeenCalled();
            });
        });

        describe("transaction management", () => {
            let contactInstance: any;
            beforeEach(() => {
                (mockContactModel as jest.Mock).mockImplementation(() => {
                    contactInstance = {
                        save: jest.fn().mockResolvedValue({}),
                    };
                    return contactInstance;
                });
            });

            it("should ensure session is always ended even if commit fails", async () => {
                // Arrange
                const dto: any = {
                    type: "client",
                    fullName: "Test Client",
                    firstName: "Test",
                    lastName: "Client",
                };
                mockSession.commitTransaction.mockRejectedValue(new Error("Commit failed"));

                // Act & Assert
                await expect(service.create(dto, mockUser)).rejects.toThrow(InternalServerErrorException);
                expect(mockSession.abortTransaction).toHaveBeenCalled();
                expect(mockSession.endSession).toHaveBeenCalled();
            });

            it("should ensure session is always ended even if abort fails", async () => {
                // Arrange
                const dto: any = {
                    type: "client",
                    fullName: "Test Client",
                    firstName: "Test",
                    lastName: "Client",
                };
                (mockContactModel as jest.Mock).mockImplementation(() => ({
                    save: jest.fn().mockRejectedValue(new Error("Save failed")),
                }));
                mockSession.abortTransaction.mockRejectedValue(new Error("Abort failed"));

                // Act & Assert
                await expect(service.create(dto, mockUser)).rejects.toThrow(InternalServerErrorException);
                expect(mockSession.endSession).toHaveBeenCalled();
            });
        });

        describe("data validation", () => {
            let contactInstance: any;
            let activityLogInstance: any;
            beforeEach(() => {
                (mockContactModel as jest.Mock).mockImplementation(() => {
                    contactInstance = {
                        save: jest.fn().mockResolvedValue({}),
                    };
                    return contactInstance;
                });
                (mockActivityLogModel as jest.Mock).mockImplementation(() => {
                    activityLogInstance = {
                        save: jest.fn().mockResolvedValue({}),
                    };
                    return activityLogInstance;
                });
            });

            it("should create contact with minimal required fields", async () => {
                // Arrange
                const dto: any = {
                    type: "client",
                    fullName: "Minimal Client",
                };

                // Act
                const result = await service.create(dto, mockUser);

                // Assert
                expect(result).toBeDefined();
                expect(result).toHaveProperty("data.id");
                expect(result).toHaveProperty("data.contact");
                expect(result).toHaveProperty("data.message", "Contact created successfully");
            });

            it("should create contact with all optional fields", async () => {
                // Arrange
                const dto: any = {
                    type: "client",
                    fullName: "Full Client",
                    firstName: "Full",
                    lastName: "Client",
                    email: "<EMAIL>",
                    phone: "1234567890",
                    businessName: "Full Business",
                    isBusiness: true,
                    street: "123 Main St",
                    city: "Metropolis",
                    state: "NY",
                    zip: "12345",
                    tags: ["tag1", "tag2"],
                };

                // Act
                const result = await service.create(dto, mockUser);

                // Assert
                expect(result).toBeDefined();
                expect(result).toHaveProperty("data.id");
                expect(result).toHaveProperty("data.contact");
                expect(result).toHaveProperty("data.message", "Contact created successfully");
            });
        });
    });

    // describe("findAll", () => {
    //     const mockUser = { companyId: "company1", memberId: "member1" };

    //     beforeEach(() => {
    //         jest.clearAllMocks();
    //     });

    //     describe("basic pagination", () => {
    //         it("should return contacts with default pagination", async () => {
    //             // Arrange
    //             const dto = {};
    //             const mockResult = [
    //                 {
    //                     paginatedResults: [
    //                         { _id: "1", fullName: "John Doe", type: "lead" },
    //                         { _id: "2", fullName: "Jane Smith", type: "client" },
    //                     ],
    //                     totalCount: [{ count: 2 }],
    //                 },
    //             ];
    //             mockContactModel.aggregate.mockResolvedValue(mockResult);

    //             // Act
    //             const result = await service.findAll(dto as any, mockUser);

    //             // Assert
    //             expect(mockContactModel.aggregate).toHaveBeenCalled();
    //             expect(result).toHaveProperty("data");
    //             expect(result.data).toHaveProperty("data");
    //             expect(result.data).toHaveProperty("pagination");
    //             expect(result.data.pagination).toEqual({
    //                 page: 1,
    //                 limit: 10,
    //                 totalItems: 2,
    //                 totalPages: 1,
    //             });
    //         });

    //         it("should return contacts with custom pagination", async () => {
    //             // Arrange
    //             const dto = { limit: 5, skip: 2 };
    //             const mockResult = [
    //                 {
    //                     paginatedResults: [{ _id: "3", fullName: "Bob Wilson", type: "prospect" }],
    //                     totalCount: [{ count: 1 }],
    //                 },
    //             ];
    //             mockContactModel.aggregate.mockResolvedValue(mockResult);

    //             // Act
    //             const result = await service.findAll(dto as any, mockUser);

    //             // Assert
    //             expect(mockContactModel.aggregate).toHaveBeenCalled();
    //             expect(result.data.pagination).toEqual({
    //                 page: 2,
    //                 limit: 5,
    //                 totalItems: 1,
    //                 totalPages: 1,
    //             });
    //         });

    //         it("should handle empty results", async () => {
    //             // Arrange
    //             const dto = {};
    //             const mockResult = [
    //                 {
    //                     paginatedResults: [],
    //                     totalCount: [{ count: 0 }],
    //                 },
    //             ];
    //             mockContactModel.aggregate.mockResolvedValue(mockResult);

    //             // Act
    //             const result = await service.findAll(dto as any, mockUser);

    //             // Assert
    //             expect(result.data.pagination).toEqual({
    //                 page: 1,
    //                 limit: 10,
    //                 totalItems: 0,
    //                 totalPages: 0,
    //             });
    //         });
    //     });

    //     describe("search functionality", () => {
    //         it("should search contacts by name", async () => {
    //             // Arrange
    //             const dto = { search: "John" };
    //             const mockResult = [
    //                 {
    //                     paginatedResults: [{ _id: "1", fullName: "John Doe" }],
    //                     totalCount: [{ count: 1 }],
    //                 },
    //             ];
    //             mockContactModel.aggregate.mockResolvedValue(mockResult);

    //             // Act
    //             const result = await service.findAll(dto as any, mockUser);

    //             // Assert
    //             expect(mockContactModel.aggregate).toHaveBeenCalled();
    //             const aggregateCall = mockContactModel.aggregate.mock.calls[0][0];
    //             expect(aggregateCall).toEqual(
    //                 expect.arrayContaining([
    //                     expect.objectContaining({
    //                         $match: expect.objectContaining({
    //                             companyId: mockUser.companyId,
    //                             deleted: false,
    //                         }),
    //                     }),
    //                     expect.objectContaining({
    //                         $match: expect.objectContaining({
    //                             $or: expect.arrayContaining([
    //                                 { fullName: { $regex: "John", $options: "i" } },
    //                                 { firstName: { $regex: "John", $options: "i" } },
    //                                 { lastName: { $regex: "John", $options: "i" } },
    //                             ]),
    //                         }),
    //                     }),
    //                 ]),
    //             );
    //         });

    //         it("should search contacts by email", async () => {
    //             // Arrange
    //             const dto = { search: "<EMAIL>" };
    //             const mockResult = [
    //                 {
    //                     paginatedResults: [{ _id: "1", email: "<EMAIL>" }],
    //                     totalCount: [{ count: 1 }],
    //                 },
    //             ];
    //             mockContactModel.aggregate.mockResolvedValue(mockResult);

    //             // Act
    //             const result = await service.findAll(dto as any, mockUser);

    //             // Assert
    //             expect(mockContactModel.aggregate).toHaveBeenCalled();
    //             const aggregateCall = mockContactModel.aggregate.mock.calls[0][0];
    //             expect(aggregateCall).toEqual(
    //                 expect.arrayContaining([
    //                     expect.objectContaining({
    //                         $match: expect.objectContaining({
    //                             $or: expect.arrayContaining([
    //                                 { email: { $regex: "<EMAIL>", $options: "i" } },
    //                             ]),
    //                         }),
    //                     }),
    //                 ]),
    //             );
    //         });

    //         it("should search contacts by phone", async () => {
    //             // Arrange
    //             const dto = { search: "1234567890" };
    //             const mockResult = [
    //                 {
    //                     paginatedResults: [{ _id: "1", phone: "1234567890" }],
    //                     totalCount: [{ count: 1 }],
    //                 },
    //             ];
    //             mockContactModel.aggregate.mockResolvedValue(mockResult);

    //             // Act
    //             const result = await service.findAll(dto as any, mockUser);

    //             // Assert
    //             expect(mockContactModel.aggregate).toHaveBeenCalled();
    //             const aggregateCall = mockContactModel.aggregate.mock.calls[0][0];
    //             expect(aggregateCall).toEqual(
    //                 expect.arrayContaining([
    //                     expect.objectContaining({
    //                         $match: expect.objectContaining({
    //                             $or: expect.arrayContaining([
    //                                 { phone: { $regex: "1234567890", $options: "i" } },
    //                             ]),
    //                         }),
    //                     }),
    //                 ]),
    //             );
    //         });
    //     });

    //     describe("filtering", () => {
    //         it("should filter by contact type", async () => {
    //             // Arrange
    //             const dto = { type: "lead" };
    //             const mockResult = [
    //                 {
    //                     paginatedResults: [{ _id: "1", type: "lead" }],
    //                     totalCount: [{ count: 1 }],
    //                 },
    //             ];
    //             mockContactModel.aggregate.mockResolvedValue(mockResult);

    //             // Act
    //             const result = await service.findAll(dto as any, mockUser);

    //             // Assert
    //             expect(mockContactModel.aggregate).toHaveBeenCalled();
    //             const aggregateCall = mockContactModel.aggregate.mock.calls[0][0];
    //             expect(aggregateCall[0].$match).toEqual(
    //                 expect.objectContaining({
    //                     companyId: mockUser.companyId,
    //                     deleted: false,
    //                     type: "lead",
    //                 }),
    //             );
    //         });

    //         it("should filter by deleted status", async () => {
    //             // Arrange
    //             const dto = { deleted: true };
    //             const mockResult = [
    //                 {
    //                     paginatedResults: [{ _id: "1", deleted: true }],
    //                     totalCount: [{ count: 1 }],
    //                 },
    //             ];
    //             mockContactModel.aggregate.mockResolvedValue(mockResult);

    //             // Act
    //             const result = await service.findAll(dto as any, mockUser);

    //             // Assert
    //             expect(mockContactModel.aggregate).toHaveBeenCalled();
    //             const aggregateCall = mockContactModel.aggregate.mock.calls[0][0];
    //             expect(aggregateCall[0].$match).toEqual(
    //                 expect.objectContaining({
    //                     companyId: mockUser.companyId,
    //                     deleted: true,
    //                 }),
    //             );
    //         });

    //         it("should handle complex filter with JSON string", async () => {
    //             // Arrange
    //             const filterData = {
    //                 filter: [
    //                     { field: "type", operator: "is", value: "lead" },
    //                     { field: "city", operator: "is", value: "New York" },
    //                 ],
    //                 logic: "AND",
    //             };
    //             const dto = { filter: JSON.stringify(filterData) };
    //             const mockResult = [
    //                 {
    //                     paginatedResults: [{ _id: "1", type: "lead", city: "New York" }],
    //                     totalCount: [{ count: 1 }],
    //                 },
    //             ];
    //             mockContactModel.aggregate.mockResolvedValue(mockResult);

    //             // Act
    //             const result = await service.findAll(dto as any, mockUser);

    //             // Assert
    //             expect(mockContactModel.aggregate).toHaveBeenCalled();
    //             const aggregateCall = mockContactModel.aggregate.mock.calls[0][0];
    //             expect(aggregateCall[0].$match).toEqual(
    //                 expect.objectContaining({
    //                     companyId: mockUser.companyId,
    //                     deleted: false,
    //                     type: "lead",
    //                     city: "New York",
    //                 }),
    //             );
    //         });

    //         it("should handle OR logic in filter", async () => {
    //             // Arrange
    //             const filterData = {
    //                 filter: [
    //                     { field: "type", operator: "is", value: "lead" },
    //                     { field: "type", operator: "is", value: "client" },
    //                 ],
    //                 logic: "OR",
    //             };
    //             const dto = { filter: JSON.stringify(filterData) };
    //             const mockResult = [
    //                 {
    //                     paginatedResults: [{ _id: "1", type: "lead" }],
    //                     totalCount: [{ count: 1 }],
    //                 },
    //             ];
    //             mockContactModel.aggregate.mockResolvedValue(mockResult);

    //             // Act
    //             const result = await service.findAll(dto as any, mockUser);

    //             // Assert
    //             expect(mockContactModel.aggregate).toHaveBeenCalled();
    //             const aggregateCall = mockContactModel.aggregate.mock.calls[0][0];
    //             expect(aggregateCall[0].$match).toEqual(
    //                 expect.objectContaining({
    //                     companyId: mockUser.companyId,
    //                     deleted: false,
    //                     $or: expect.arrayContaining([{ type: "lead" }, { type: "client" }]),
    //                 }),
    //             );
    //         });
    //     });

    //     describe("duplicate detection", () => {
    //         it("should detect duplicates by email field", async () => {
    //             // Arrange
    //             const filterData = {
    //                 filter: [{ field: "email", operator: "has_duplicates", value: "email" }],
    //                 logic: "AND",
    //             };
    //             const dto = { filter: JSON.stringify(filterData) };
    //             const mockResult = [
    //                 {
    //                     paginatedResults: [
    //                         { _id: "1", email: "<EMAIL>" },
    //                         { _id: "2", email: "<EMAIL>" },
    //                     ],
    //                     totalCount: [{ count: 2 }],
    //                 },
    //             ];
    //             mockContactModel.aggregate.mockResolvedValue(mockResult);

    //             // Act
    //             const result = await service.findAll(dto as any, mockUser);

    //             // Assert
    //             expect(mockContactModel.aggregate).toHaveBeenCalled();
    //             const aggregateCall = mockContactModel.aggregate.mock.calls[0][0];
    //             expect(aggregateCall).toEqual(
    //                 expect.arrayContaining([
    //                     expect.objectContaining({
    //                         $match: expect.objectContaining({
    //                             email: { $exists: true, $nin: [null, ""] },
    //                         }),
    //                     }),
    //                     expect.objectContaining({
    //                         $group: expect.objectContaining({
    //                             _id: "$email",
    //                         }),
    //                     }),
    //                     expect.objectContaining({
    //                         $match: expect.objectContaining({
    //                             count: { $gt: 1 },
    //                         }),
    //                     }),
    //                 ]),
    //             );
    //         });

    //         it("should detect duplicates by phone field", async () => {
    //             // Arrange
    //             const filterData = {
    //                 filter: [{ field: "phone", operator: "has_duplicates", value: "phone" }],
    //                 logic: "AND",
    //             };
    //             const dto = { filter: JSON.stringify(filterData) };
    //             const mockResult = [
    //                 {
    //                     paginatedResults: [
    //                         { _id: "1", phone: "1234567890" },
    //                         { _id: "2", phone: "1234567890" },
    //                     ],
    //                     totalCount: [{ count: 2 }],
    //                 },
    //             ];
    //             mockContactModel.aggregate.mockResolvedValue(mockResult);

    //             // Act
    //             const result = await service.findAll(dto as any, mockUser);

    //             // Assert
    //             expect(mockContactModel.aggregate).toHaveBeenCalled();
    //             const aggregateCall = mockContactModel.aggregate.mock.calls[0][0];
    //             expect(aggregateCall).toEqual(
    //                 expect.arrayContaining([
    //                     expect.objectContaining({
    //                         $match: expect.objectContaining({
    //                             phone: { $exists: true, $nin: [null, ""] },
    //                         }),
    //                     }),
    //                     expect.objectContaining({
    //                         $group: expect.objectContaining({
    //                             _id: "$phone",
    //                         }),
    //                     }),
    //                 ]),
    //             );
    //         });
    //     });

    //     describe("aggregation pipeline", () => {
    //         it("should include lead source lookup", async () => {
    //             // Arrange
    //             const dto = {};
    //             const mockResult = [
    //                 {
    //                     paginatedResults: [{ _id: "1", leadSourceName: "Website" }],
    //                     totalCount: [{ count: 1 }],
    //                 },
    //             ];
    //             mockContactModel.aggregate.mockResolvedValue(mockResult);

    //             // Act
    //             const result = await service.findAll(dto as any, mockUser);

    //             // Assert
    //             expect(mockContactModel.aggregate).toHaveBeenCalled();
    //             const aggregateCall = mockContactModel.aggregate.mock.calls[0][0];
    //             expect(aggregateCall).toEqual(
    //                 expect.arrayContaining([
    //                     expect.objectContaining({
    //                         $lookup: expect.objectContaining({
    //                             from: "LeadSource",
    //                             localField: "leadSourceId",
    //                             foreignField: "_id",
    //                             as: "leadSource",
    //                         }),
    //                     }),
    //                     expect.objectContaining({
    //                         $unwind: expect.objectContaining({
    //                             path: "$leadSource",
    //                             preserveNullAndEmptyArrays: true,
    //                         }),
    //                     }),
    //                     expect.objectContaining({
    //                         $project: expect.objectContaining({
    //                             leadSourceName: "$leadSource.name",
    //                         }),
    //                     }),
    //                 ]),
    //             );
    //         });

    //         it("should sort by creation date descending", async () => {
    //             // Arrange
    //             const dto = {};
    //             const mockResult = [
    //                 {
    //                     paginatedResults: [{ _id: "1", createdAt: new Date() }],
    //                     totalCount: [{ count: 1 }],
    //                 },
    //             ];
    //             mockContactModel.aggregate.mockResolvedValue(mockResult);

    //             // Act
    //             const result = await service.findAll(dto as any, mockUser);

    //             // Assert
    //             expect(mockContactModel.aggregate).toHaveBeenCalled();
    //             const aggregateCall = mockContactModel.aggregate.mock.calls[0][0];
    //             expect(aggregateCall).toEqual(
    //                 expect.arrayContaining([
    //                     expect.objectContaining({
    //                         $sort: expect.objectContaining({
    //                             createdAt: -1,
    //                         }),
    //                     }),
    //                 ]),
    //             );
    //         });

    //         it("should include proper projection fields", async () => {
    //             // Arrange
    //             const dto = {};
    //             const mockResult = [
    //                 {
    //                     paginatedResults: [{ _id: "1", fullName: "John Doe" }],
    //                     totalCount: [{ count: 1 }],
    //                 },
    //             ];
    //             mockContactModel.aggregate.mockResolvedValue(mockResult);

    //             // Act
    //             const result = await service.findAll(dto as any, mockUser);

    //             // Assert
    //             expect(mockContactModel.aggregate).toHaveBeenCalled();
    //             const aggregateCall = mockContactModel.aggregate.mock.calls[0][0];
    //             const projectStage = aggregateCall.find((stage) => stage.$project);
    //             expect(projectStage.$project).toEqual(
    //                 expect.objectContaining({
    //                     fullName: 1,
    //                     businessName: 1,
    //                     isBusiness: 1,
    //                     phone: 1,
    //                     email: 1,
    //                     type: 1,
    //                     street: 1,
    //                     city: 1,
    //                     state: 1,
    //                     zip: 1,
    //                     leadSourceName: "$leadSource.name",
    //                     createdAt: 1,
    //                     dateReceived: 1,
    //                     firstName: 1,
    //                     lastName: 1,
    //                     tags: 1,
    //                 }),
    //             );
    //         });
    //     });

    //     describe("error handling", () => {
    //         it("should throw InternalServerErrorException when aggregation fails", async () => {
    //             // Arrange
    //             const dto = {};
    //             const error = new Error("Database connection failed");
    //             mockContactModel.aggregate.mockRejectedValue(error);

    //             // Act & Assert
    //             await expect(service.findAll(dto as any, mockUser)).rejects.toThrow(
    //                 InternalServerErrorException,
    //             );
    //         });

    //         it("should re-throw HttpException when it occurs", async () => {
    //             // Arrange
    //             const dto = {};
    //             const httpError = new BadRequestException("Invalid filter");
    //             mockContactModel.aggregate.mockRejectedValue(httpError);

    //             // Act & Assert
    //             await expect(service.findAll(dto as any, mockUser)).rejects.toThrow(BadRequestException);
    //         });

    //         it("should handle invalid JSON filter gracefully", async () => {
    //             // Arrange
    //             const dto = { filter: "invalid json" };
    //             const mockResult = [
    //                 {
    //                     paginatedResults: [],
    //                     totalCount: [{ count: 0 }],
    //                 },
    //             ];
    //             mockContactModel.aggregate.mockResolvedValue(mockResult);

    //             // Act & Assert
    //             await expect(service.findAll(dto as any, mockUser)).rejects.toThrow();
    //         });
    //     });

    //     describe("edge cases", () => {
    //         it("should handle null filter gracefully", async () => {
    //             // Arrange
    //             const dto = { filter: null };
    //             const mockResult = [
    //                 {
    //                     paginatedResults: [],
    //                     totalCount: [{ count: 0 }],
    //                 },
    //             ];
    //             mockContactModel.aggregate.mockResolvedValue(mockResult);

    //             // Act
    //             const result = await service.findAll(dto as any, mockUser);

    //             // Assert
    //             expect(mockContactModel.aggregate).toHaveBeenCalled();
    //             expect(result).toBeDefined();
    //         });

    //         it("should handle undefined filter gracefully", async () => {
    //             // Arrange
    //             const dto = { filter: undefined };
    //             const mockResult = [
    //                 {
    //                     paginatedResults: [],
    //                     totalCount: [{ count: 0 }],
    //                 },
    //             ];
    //             mockContactModel.aggregate.mockResolvedValue(mockResult);

    //             // Act
    //             const result = await service.findAll(dto as any, mockUser);

    //             // Assert
    //             expect(mockContactModel.aggregate).toHaveBeenCalled();
    //             expect(result).toBeDefined();
    //         });

    //         it("should handle large pagination values", async () => {
    //             // Arrange
    //             const dto = { limit: 1000, skip: 100 };
    //             const mockResult = [
    //                 {
    //                     paginatedResults: [],
    //                     totalCount: [{ count: 0 }],
    //                 },
    //             ];
    //             mockContactModel.aggregate.mockResolvedValue(mockResult);

    //             // Act
    //             const result = await service.findAll(dto as any, mockUser);

    //             // Assert
    //             expect(mockContactModel.aggregate).toHaveBeenCalled();
    //             const aggregateCall = mockContactModel.aggregate.mock.calls[0][0];
    //             const facetStage = aggregateCall.find((stage) => stage.$facet);
    //             expect(facetStage.$facet.paginatedResults).toEqual(
    //                 expect.arrayContaining([
    //                     { $skip: 99000 }, // (100 - 1) * 1000
    //                     { $limit: 1000 },
    //                 ]),
    //             );
    //         });
    //     });
    // });

    // describe("findOne", () => {
    //     const mockUser = { companyId: "company1", memberId: "member1" };

    //     beforeEach(() => {
    //         jest.clearAllMocks();
    //     });

    //     describe("successful contact retrieval", () => {
    //         it("should return a contact with basic information", async () => {
    //             // Arrange
    //             const contactId = "contact1";
    //             const mockContact = {
    //                 _id: contactId,
    //                 fullName: "John Doe",
    //                 type: "lead",
    //                 email: "<EMAIL>",
    //                 phone: "1234567890",
    //                 comments: [],
    //             };
    //             const mockOpportunities = [];

    //             mockContactModel.aggregate.mockResolvedValue([mockContact]);
    //             mockOpportunityModel.aggregate.mockResolvedValue(mockOpportunities);

    //             // Act
    //             const result = await service.findOne(contactId, mockUser);

    //             // Assert
    //             expect(mockContactModel.aggregate).toHaveBeenCalled();
    //             expect(mockOpportunityModel.aggregate).toHaveBeenCalled();
    //             expect(result).toHaveProperty("data");
    //             expect(result.data).toHaveProperty("contact");
    //             expect(result.data.contact).toEqual(mockContact);
    //         });

    //         it("should return a contact with opportunities and comments", async () => {
    //             // Arrange
    //             const contactId = "contact1";
    //             const mockContact = {
    //                 _id: contactId,
    //                 fullName: "John Doe",
    //                 type: "lead",
    //                 comments: [
    //                     { _id: "comment1", body: "Test comment", createdBy: "user1" }
    //                 ],
    //                 users: [
    //                     { _id: "user1", name: "John User" }
    //                 ]
    //             };
    //             const mockOpportunities = [
    //                 {
    //                     oppId: "opp1",
    //                     PO: "PO123",
    //                     num: "OPP001",
    //                     stageGroup: "Leads",
    //                     comments: [
    //                         { _id: "oppComment1", body: "Opportunity comment", createdBy: "user2" }
    //                     ],
    //                     nextAction: "Follow up",
    //                     users: [
    //                         { _id: "user2", name: "Jane User" }
    //                     ]
    //                 }
    //             ];

    //             mockContactModel.aggregate.mockResolvedValue([mockContact]);
    //             mockOpportunityModel.aggregate.mockResolvedValue(mockOpportunities);

    //             // Act
    //             const result = await service.findOne(contactId, mockUser);

    //             // Assert
    //             expect(result.data.contact).toHaveProperty("oppsComments");
    //             expect(result.data.contact).toHaveProperty("oppnextAction");
    //             expect(result.data.contact.oppsComments).toHaveLength(1);
    //             expect(result.data.contact.oppsComments[0]).toHaveProperty("oppId", "opp1");
    //             expect(result.data.contact.oppsComments[0]).toHaveProperty("PO", "PO123");
    //             expect(result.data.contact.oppsComments[0]).toHaveProperty("stageGroup", "Leads");
    //         });

    //         it("should return a contact with referrer information", async () => {
    //             // Arrange
    //             const contactId = "contact1";
    //             const mockContact = {
    //                 _id: contactId,
    //                 fullName: "John Doe",
    //                 referredBy: "referrer1",
    //                 referrer: {
    //                     _id: "referrer1",
    //                     name: "Referrer Name"
    //                 }
    //             };
    //             const mockOpportunities = [];

    //             mockContactModel.aggregate.mockResolvedValue([mockContact]);
    //             mockOpportunityModel.aggregate.mockResolvedValue(mockOpportunities);

    //             // Act
    //             const result = await service.findOne(contactId, mockUser);

    //             // Assert
    //             expect(result.data.contact).toHaveProperty("referrer");
    //             expect(result.data.contact.referrer).toHaveProperty("name", "Referrer Name");
    //         });
    //     });

    //     describe("aggregation pipeline testing", () => {
    //         it("should call contact aggregation with correct pipeline", async () => {
    //             // Arrange
    //             const contactId = "contact1";
    //             const mockContact = { _id: contactId, fullName: "John Doe" };
    //             const mockOpportunities = [];

    //             mockContactModel.aggregate.mockResolvedValue([mockContact]);
    //             mockOpportunityModel.aggregate.mockResolvedValue(mockOpportunities);

    //             // Act
    //             await service.findOne(contactId, mockUser);

    //             // Assert
    //             expect(mockContactModel.aggregate).toHaveBeenCalledWith(
    //                 expect.arrayContaining([
    //                     expect.objectContaining({
    //                         $match: {
    //                             _id: contactId,
    //                             companyId: mockUser.companyId
    //                         }
    //                     }),
    //                     expect.objectContaining({
    //                         $lookup: expect.objectContaining({
    //                             from: "Contact",
    //                             as: "referrer"
    //                         })
    //                     }),
    //                     expect.objectContaining({
    //                         $lookup: expect.objectContaining({
    //                             from: "Member",
    //                             as: "users"
    //                         })
    //                     })
    //                 ])
    //             );
    //         });

    //         it("should call opportunity aggregation with correct pipeline", async () => {
    //             // Arrange
    //             const contactId = "contact1";
    //             const mockContact = { _id: contactId, fullName: "John Doe" };
    //             const mockOpportunities = [];

    //             mockContactModel.aggregate.mockResolvedValue([mockContact]);
    //             mockOpportunityModel.aggregate.mockResolvedValue(mockOpportunities);

    //             // Act
    //             await service.findOne(contactId, mockUser);

    //             // Assert
    //             expect(mockOpportunityModel.aggregate).toHaveBeenCalledWith(
    //                 expect.arrayContaining([
    //                     expect.objectContaining({
    //                         $match: {
    //                             contactId: contactId,
    //                             companyId: mockUser.companyId
    //                         }
    //                     }),
    //                     expect.objectContaining({
    //                         $lookup: expect.objectContaining({
    //                             from: "CrmStage",
    //                             as: "stageData"
    //                         })
    //                     }),
    //                     expect.objectContaining({
    //                         $lookup: expect.objectContaining({
    //                             from: "Member",
    //                             as: "users"
    //                         })
    //                     }),
    //                     expect.objectContaining({
    //                         $project: expect.objectContaining({
    //                             oppId: "$_id",
    //                             stageGroup: "$stageData.stageGroup"
    //                         })
    //                     })
    //                 ])
    //             );
    //         });
    //     });

    //     describe("comment processing", () => {
    //         it("should process contact comments with user names", async () => {
    //             // Arrange
    //             const contactId = "contact1";
    //             const mockContact = {
    //                 _id: contactId,
    //                 fullName: "John Doe",
    //                 comments: [
    //                     { _id: "comment1", body: "Test comment", createdBy: "user1" }
    //                 ],
    //                 users: [
    //                     { _id: "user1", name: "John User" }
    //                 ]
    //             };
    //             const mockOpportunities = [];

    //             mockContactModel.aggregate.mockResolvedValue([mockContact]);
    //             mockOpportunityModel.aggregate.mockResolvedValue(mockOpportunities);

    //             // Act
    //             const result = await service.findOne(contactId, mockUser);

    //             // Assert
    //             expect(result.data.contact.comments[0]).toHaveProperty("name", "John User");
    //         });

    //         it("should process opportunity comments with user names", async () => {
    //             // Arrange
    //             const contactId = "contact1";
    //             const mockContact = {
    //                 _id: contactId,
    //                 fullName: "John Doe",
    //                 comments: [],
    //                 users: []
    //             };
    //             const mockOpportunities = [
    //                 {
    //                     oppId: "opp1",
    //                     comments: [
    //                         { _id: "oppComment1", body: "Opportunity comment", createdBy: "user2" }
    //                     ],
    //                     users: [
    //                         { _id: "user2", name: "Jane User" }
    //                     ]
    //                 }
    //             ];

    //             mockContactModel.aggregate.mockResolvedValue([mockContact]);
    //             mockOpportunityModel.aggregate.mockResolvedValue(mockOpportunities);

    //             // Act
    //             const result = await service.findOne(contactId, mockUser);

    //             // Assert
    //             expect(result.data.contact.oppsComments[0]).toHaveProperty("name", "Jane User");
    //         });

    //         it("should merge opportunity comments into contact comments", async () => {
    //             // Arrange
    //             const contactId = "contact1";
    //             const mockContact = {
    //                 _id: contactId,
    //                 fullName: "John Doe",
    //                 comments: [
    //                     { _id: "comment1", body: "Contact comment", createdBy: "user1" }
    //                 ],
    //                 users: [
    //                     { _id: "user1", name: "John User" }
    //                 ]
    //             };
    //             const mockOpportunities = [
    //                 {
    //                     oppId: "opp1",
    //                     comments: [
    //                         { _id: "oppComment1", body: "Opportunity comment", createdBy: "user2" }
    //                     ],
    //                     users: [
    //                         { _id: "user2", name: "Jane User" }
    //                     ]
    //                 }
    //             ];

    //             mockContactModel.aggregate.mockResolvedValue([mockContact]);
    //             mockOpportunityModel.aggregate.mockResolvedValue(mockOpportunities);

    //             // Act
    //             const result = await service.findOne(contactId, mockUser);

    //             // Assert
    //             expect(result.data.contact.comments).toHaveLength(2);
    //             expect(result.data.contact.comments[0]).toHaveProperty("body", "Contact comment");
    //             expect(result.data.contact.comments[1]).toHaveProperty("body", "Opportunity comment");
    //             expect(result.data.contact.comments[1]).toHaveProperty("oppId", "opp1");
    //         });

    //         it("should handle empty comments arrays", async () => {
    //             // Arrange
    //             const contactId = "contact1";
    //             const mockContact = {
    //                 _id: contactId,
    //                 fullName: "John Doe",
    //                 comments: null,
    //                 users: []
    //             };
    //             const mockOpportunities = [
    //                 {
    //                     oppId: "opp1",
    //                     comments: null,
    //                     users: []
    //                 }
    //             ];

    //             mockContactModel.aggregate.mockResolvedValue([mockContact]);
    //             mockOpportunityModel.aggregate.mockResolvedValue(mockOpportunities);

    //             // Act
    //             const result = await service.findOne(contactId, mockUser);

    //             // Assert
    //             expect(result.data.contact).toBeDefined();
    //             expect(result.data.contact.oppsComments).toHaveLength(0);
    //         });
    //     });

    //     describe("user mapping", () => {
    //         it("should create a unified user map from contact and opportunities", async () => {
    //             // Arrange
    //             const contactId = "contact1";
    //             const mockContact = {
    //                 _id: contactId,
    //                 fullName: "John Doe",
    //                 comments: [
    //                     { _id: "comment1", body: "Contact comment", createdBy: "user1" }
    //                 ],
    //                 users: [
    //                     { _id: "user1", name: "John User" }
    //                 ]
    //             };
    //             const mockOpportunities = [
    //                 {
    //                     oppId: "opp1",
    //                     comments: [
    //                         { _id: "oppComment1", body: "Opportunity comment", createdBy: "user2" }
    //                     ],
    //                     users: [
    //                         { _id: "user2", name: "Jane User" }
    //                     ]
    //                 }
    //             ];

    //             mockContactModel.aggregate.mockResolvedValue([mockContact]);
    //             mockOpportunityModel.aggregate.mockResolvedValue(mockOpportunities);

    //             // Act
    //             const result = await service.findOne(contactId, mockUser);

    //             // Assert
    //             expect(result.data.contact.comments[0]).toHaveProperty("name", "John User");
    //             expect(result.data.contact.oppsComments[0]).toHaveProperty("name", "Jane User");
    //         });

    //         it("should handle duplicate users in contact and opportunities", async () => {
    //             // Arrange
    //             const contactId = "contact1";
    //             const mockContact = {
    //                 _id: contactId,
    //                 fullName: "John Doe",
    //                 comments: [
    //                     { _id: "comment1", body: "Contact comment", createdBy: "user1" }
    //                 ],
    //                 users: [
    //                     { _id: "user1", name: "John User" }
    //                 ]
    //             };
    //             const mockOpportunities = [
    //                 {
    //                     oppId: "opp1",
    //                     comments: [
    //                         { _id: "oppComment1", body: "Opportunity comment", createdBy: "user1" }
    //                     ],
    //                     users: [
    //                         { _id: "user1", name: "John User" }
    //                     ]
    //                 }
    //             ];

    //             mockContactModel.aggregate.mockResolvedValue([mockContact]);
    //             mockOpportunityModel.aggregate.mockResolvedValue(mockOpportunities);

    //             // Act
    //             const result = await service.findOne(contactId, mockUser);

    //             // Assert
    //             expect(result.data.contact.comments[0]).toHaveProperty("name", "John User");
    //             expect(result.data.contact.oppsComments[0]).toHaveProperty("name", "John User");
    //         });
    //     });

    //     describe("opportunity data processing", () => {
    //         it("should extract next actions from opportunities", async () => {
    //             // Arrange
    //             const contactId = "contact1";
    //             const mockContact = {
    //                 _id: contactId,
    //                 fullName: "John Doe",
    //                 comments: [],
    //                 users: []
    //             };
    //             const mockOpportunities = [
    //                 {
    //                     oppId: "opp1",
    //                     nextAction: "Follow up call"
    //                 },
    //                 {
    //                     oppId: "opp2",
    //                     nextAction: "Send proposal"
    //                 }
    //             ];

    //             mockContactModel.aggregate.mockResolvedValue([mockContact]);
    //             mockOpportunityModel.aggregate.mockResolvedValue(mockOpportunities);

    //             // Act
    //             const result = await service.findOne(contactId, mockUser);

    //             // Assert
    //             expect(result.data.contact).toHaveProperty("oppnextAction");
    //             expect(result.data.contact.oppnextAction).toEqual(["Follow up call", "Send proposal"]);
    //         });

    //         it("should flatten opportunity comments with opportunity data", async () => {
    //             // Arrange
    //             const contactId = "contact1";
    //             const mockContact = {
    //                 _id: contactId,
    //                 fullName: "John Doe",
    //                 comments: [],
    //                 users: []
    //             };
    //             const mockOpportunities = [
    //                 {
    //                     oppId: "opp1",
    //                     PO: "PO123",
    //                     num: "OPP001",
    //                     stageGroup: "Leads",
    //                     comments: [
    //                         { _id: "comment1", body: "Comment 1", createdBy: "user1" },
    //                         { _id: "comment2", body: "Comment 2", createdBy: "user2" }
    //                     ],
    //                     users: [
    //                         { _id: "user1", name: "John User" },
    //                         { _id: "user2", name: "Jane User" }
    //                     ]
    //                 }
    //             ];

    //             mockContactModel.aggregate.mockResolvedValue([mockContact]);
    //             mockOpportunityModel.aggregate.mockResolvedValue(mockOpportunities);

    //             // Act
    //             const result = await service.findOne(contactId, mockUser);

    //             // Assert
    //             expect(result.data.contact.oppsComments).toHaveLength(2);
    //             expect(result.data.contact.oppsComments[0]).toHaveProperty("oppId", "opp1");
    //             expect(result.data.contact.oppsComments[0]).toHaveProperty("PO", "PO123");
    //             expect(result.data.contact.oppsComments[0]).toHaveProperty("num", "OPP001");
    //             expect(result.data.contact.oppsComments[0]).toHaveProperty("stageGroup", "Leads");
    //             expect(result.data.contact.oppsComments[0]).toHaveProperty("body", "Comment 1");
    //             expect(result.data.contact.oppsComments[1]).toHaveProperty("body", "Comment 2");
    //         });
    //     });

    //     describe("error handling", () => {
    //         it("should throw NotFoundException when contact is not found", async () => {
    //             // Arrange
    //             const contactId = "nonexistent";
    //             const mockOpportunities = [];

    //             mockContactModel.aggregate.mockResolvedValue([]);
    //             mockOpportunityModel.aggregate.mockResolvedValue(mockOpportunities);

    //             // Act & Assert
    //             await expect(service.findOne(contactId, mockUser)).rejects.toThrow(NotFoundException);
    //         });

    //         it("should throw InternalServerErrorException when contact aggregation fails", async () => {
    //             // Arrange
    //             const contactId = "contact1";
    //             const error = new Error("Database connection failed");
    //             mockContactModel.aggregate.mockRejectedValue(error);

    //             // Act & Assert
    //             await expect(service.findOne(contactId, mockUser)).rejects.toThrow(InternalServerErrorException);
    //         });

    //         it("should throw InternalServerErrorException when opportunity aggregation fails", async () => {
    //             // Arrange
    //             const contactId = "contact1";
    //             const mockContact = { _id: contactId, fullName: "John Doe" };
    //             const error = new Error("Database connection failed");

    //             mockContactModel.aggregate.mockResolvedValue([mockContact]);
    //             mockOpportunityModel.aggregate.mockRejectedValue(error);

    //             // Act & Assert
    //             await expect(service.findOne(contactId, mockUser)).rejects.toThrow(InternalServerErrorException);
    //         });

    //         it("should re-throw HttpException when it occurs", async () => {
    //             // Arrange
    //             const contactId = "contact1";
    //             const httpError = new BadRequestException("Invalid contact ID");
    //             mockContactModel.aggregate.mockRejectedValue(httpError);

    //             // Act & Assert
    //             await expect(service.findOne(contactId, mockUser)).rejects.toThrow(BadRequestException);
    //         });
    //     });

    //     describe("edge cases", () => {
    //         it("should handle contact with no opportunities", async () => {
    //             // Arrange
    //             const contactId = "contact1";
    //             const mockContact = {
    //                 _id: contactId,
    //                 fullName: "John Doe",
    //                 comments: [],
    //                 users: []
    //             };
    //             const mockOpportunities = [];

    //             mockContactModel.aggregate.mockResolvedValue([mockContact]);
    //             mockOpportunityModel.aggregate.mockResolvedValue(mockOpportunities);

    //             // Act
    //             const result = await service.findOne(contactId, mockUser);

    //             // Assert
    //             expect(result.data.contact).toHaveProperty("oppsComments");
    //             expect(result.data.contact).toHaveProperty("oppnextAction");
    //             expect(result.data.contact.oppsComments).toHaveLength(0);
    //             expect(result.data.contact.oppnextAction).toHaveLength(0);
    //         });

    //         it("should handle contact with no comments", async () => {
    //             // Arrange
    //             const contactId = "contact1";
    //             const mockContact = {
    //                 _id: contactId,
    //                 fullName: "John Doe",
    //                 comments: [],
    //                 users: []
    //             };
    //             const mockOpportunities = [];

    //             mockContactModel.aggregate.mockResolvedValue([mockContact]);
    //             mockOpportunityModel.aggregate.mockResolvedValue(mockOpportunities);

    //             // Act
    //             const result = await service.findOne(contactId, mockUser);

    //             // Assert
    //             expect(result.data.contact).toBeDefined();
    //             expect(result.data.contact.comments).toHaveLength(0);
    //         });

    //         it("should handle opportunities with no comments", async () => {
    //             // Arrange
    //             const contactId = "contact1";
    //             const mockContact = {
    //                 _id: contactId,
    //                 fullName: "John Doe",
    //                 comments: [],
    //                 users: []
    //             };
    //             const mockOpportunities = [
    //                 {
    //                     oppId: "opp1",
    //                     comments: [],
    //                     users: []
    //                 }
    //             ];

    //             mockContactModel.aggregate.mockResolvedValue([mockContact]);
    //             mockOpportunityModel.aggregate.mockResolvedValue(mockOpportunities);

    //             // Act
    //             const result = await service.findOne(contactId, mockUser);

    //             // Assert
    //             expect(result.data.contact.oppsComments).toHaveLength(0);
    //         });

    //         it("should handle contact with null referrer", async () => {
    //             // Arrange
    //             const contactId = "contact1";
    //             const mockContact = {
    //                 _id: contactId,
    //                 fullName: "John Doe",
    //                 referredBy: null,
    //                 referrer: null,
    //                 comments: [],
    //                 users: []
    //             };
    //             const mockOpportunities = [];

    //             mockContactModel.aggregate.mockResolvedValue([mockContact]);
    //             mockOpportunityModel.aggregate.mockResolvedValue(mockOpportunities);

    //             // Act
    //             const result = await service.findOne(contactId, mockUser);

    //             // Assert
    //             expect(result.data.contact).toBeDefined();
    //             expect(result.data.contact.referrer).toBeNull();
    //         });

    //         it("should handle comments with missing user information", async () => {
    //             // Arrange
    //             const contactId = "contact1";
    //             const mockContact = {
    //                 _id: contactId,
    //                 fullName: "John Doe",
    //                 comments: [
    //                     { _id: "comment1", body: "Test comment", createdBy: "unknownUser" }
    //                 ],
    //                 users: [
    //                     { _id: "user1", name: "John User" }
    //                 ]
    //             };
    //             const mockOpportunities = [];

    //             mockContactModel.aggregate.mockResolvedValue([mockContact]);
    //             mockOpportunityModel.aggregate.mockResolvedValue(mockOpportunities);

    //             // Act
    //             const result = await service.findOne(contactId, mockUser);

    //             // Assert
    //             expect(result.data.contact.comments[0]).not.toHaveProperty("name");
    //         });
    //     });
    // });

    // describe("update", () => {
    //     const mockUser = { companyId: "company1", memberId: "member1" };

    //     beforeEach(() => {
    //         jest.clearAllMocks();
    //     });

    //     describe("successful updates", () => {
    //         it("should update a contact with basic information", async () => {
    //             // Arrange
    //             const contactId = "contact1";
    //             const updateDto = {
    //                 fullName: "Updated Name",
    //                 firstName: "Updated",
    //                 lastName: "Name",
    //                 email: "<EMAIL>",
    //                 phone: "9876543210"
    //             };
    //             const mockUpdatedContact = {
    //                 _id: contactId,
    //                 ...updateDto,
    //                 companyId: mockUser.companyId,
    //                 deleted: false
    //             };

    //             mockContactModel.findOneAndUpdate.mockResolvedValue(mockUpdatedContact);

    //             // Act
    //             const result = await service.update(contactId, updateDto as any, mockUser);

    //             // Assert
    //             expect(mockContactModel.findOneAndUpdate).toHaveBeenCalledWith(
    //                 {
    //                     _id: contactId,
    //                     companyId: mockUser.companyId,
    //                     deleted: false
    //                 },
    //                 {
    //                     $set: updateDto
    //                 },
    //                 { new: true }
    //             );
    //             expect(result).toHaveProperty("data");
    //             expect(result.data).toHaveProperty("contact");
    //             expect(result.data.contact).toEqual(mockUpdatedContact);
    //         });

    //         it("should update a contact with all fields", async () => {
    //             // Arrange
    //             const contactId = "contact1";
    //             const updateDto = {
    //                 fullName: "Full Updated Contact",
    //                 firstName: "Full",
    //                 lastName: "Updated",
    //                 street: "456 Updated St",
    //                 city: "Updated City",
    //                 state: "UC",
    //                 zip: "54321",
    //                 fullAddress: "456 Updated St, Updated City, UC 54321",
    //                 lat: 41.7128,
    //                 long: -75.0060,
    //                 phone: "5551234567",
    //                 email: "<EMAIL>",
    //                 appointmentSetter: "uuid1",
    //                 salesPersonId: "uuid2",
    //                 projectManagerId: "uuid3",
    //                 workType: "commercial",
    //                 leadSourceId: "uuid4",
    //                 campaignId: "uuid5",
    //                 referrer: "Updated Website",
    //                 note: ["Updated Note 1", "Updated Note 2"],
    //                 automations: ["Updated Auto 1", "Updated Auto 2"],
    //                 invalidLeadReason: "Updated reason",
    //                 lostReason: "Updated lost reason",
    //                 dateOfBirth: new Date("1985-05-15"),
    //                 comments: [{ body: "Updated comment" }],
    //                 referredBy: "uuid6",
    //                 type: "client",
    //                 status: "active"
    //             };
    //             const mockUpdatedContact = {
    //                 _id: contactId,
    //                 ...updateDto,
    //                 companyId: mockUser.companyId,
    //                 deleted: false
    //             };

    //             mockContactModel.findOneAndUpdate.mockResolvedValue(mockUpdatedContact);

    //             // Act
    //             const result = await service.update(contactId, updateDto as any, mockUser);

    //             // Assert
    //             expect(mockContactModel.findOneAndUpdate).toHaveBeenCalledWith(
    //                 {
    //                     _id: contactId,
    //                     companyId: mockUser.companyId,
    //                     deleted: false
    //                 },
    //                 {
    //                     $set: updateDto
    //                 },
    //                 { new: true }
    //             );
    //             expect(result.data.contact).toEqual(mockUpdatedContact);
    //         });

    //         it("should update only specific fields", async () => {
    //             // Arrange
    //             const contactId = "contact1";
    //             const updateDto = {
    //                 email: "<EMAIL>",
    //                 phone: "1112223333"
    //             };
    //             const mockUpdatedContact = {
    //                 _id: contactId,
    //                 fullName: "John Doe",
    //                 email: "<EMAIL>",
    //                 phone: "1112223333",
    //                 companyId: mockUser.companyId,
    //                 deleted: false
    //             };

    //             mockContactModel.findOneAndUpdate.mockResolvedValue(mockUpdatedContact);

    //             // Act
    //             const result = await service.update(contactId, updateDto as any, mockUser);

    //             // Assert
    //             expect(mockContactModel.findOneAndUpdate).toHaveBeenCalledWith(
    //                 {
    //                     _id: contactId,
    //                     companyId: mockUser.companyId,
    //                     deleted: false
    //                 },
    //                 {
    //                     $set: updateDto
    //                 },
    //                 { new: true }
    //             );
    //             expect(result.data.contact.email).toBe("<EMAIL>");
    //             expect(result.data.contact.phone).toBe("1112223333");
    //         });
    //     });

    //     describe("error handling", () => {
    //         it("should throw NotFoundException when contact is not found", async () => {
    //             // Arrange
    //             const contactId = "nonexistent";
    //             const updateDto = { fullName: "Updated Name" };

    //             mockContactModel.findOneAndUpdate.mockResolvedValue(null);

    //             // Act & Assert
    //             await expect(service.update(contactId, updateDto as any, mockUser)).rejects.toThrow(NotFoundException);
    //         });

    //         it("should throw NotFoundException when contact is already deleted", async () => {
    //             // Arrange
    //             const contactId = "deleted-contact";
    //             const updateDto = { fullName: "Updated Name" };

    //             mockContactModel.findOneAndUpdate.mockResolvedValue(null);

    //             // Act & Assert
    //             await expect(service.update(contactId, updateDto as any, mockUser)).rejects.toThrow(NotFoundException);
    //         });

    //         it("should throw InternalServerErrorException when database operation fails", async () => {
    //             // Arrange
    //             const contactId = "contact1";
    //             const updateDto = { fullName: "Updated Name" };
    //             const error = new Error("Database connection failed");

    //             mockContactModel.findOneAndUpdate.mockRejectedValue(error);

    //             // Act & Assert
    //             await expect(service.update(contactId, updateDto as any, mockUser)).rejects.toThrow(InternalServerErrorException);
    //         });

    //         it("should re-throw HttpException when it occurs", async () => {
    //             // Arrange
    //             const contactId = "contact1";
    //             const updateDto = { fullName: "Updated Name" };
    //             const httpError = new BadRequestException("Invalid update data");

    //             mockContactModel.findOneAndUpdate.mockRejectedValue(httpError);

    //             // Act & Assert
    //             await expect(service.update(contactId, updateDto as any, mockUser)).rejects.toThrow(BadRequestException);
    //         });
    //     });

    //     describe("edge cases", () => {
    //         it("should handle empty update object", async () => {
    //             // Arrange
    //             const contactId = "contact1";
    //             const updateDto = {};
    //             const mockUpdatedContact = {
    //                 _id: contactId,
    //                 fullName: "John Doe",
    //                 companyId: mockUser.companyId,
    //                 deleted: false
    //             };

    //             mockContactModel.findOneAndUpdate.mockResolvedValue(mockUpdatedContact);

    //             // Act
    //             const result = await service.update(contactId, updateDto as any, mockUser);

    //             // Assert
    //             expect(mockContactModel.findOneAndUpdate).toHaveBeenCalledWith(
    //                 {
    //                     _id: contactId,
    //                     companyId: mockUser.companyId,
    //                     deleted: false
    //                 },
    //                 {
    //                     $set: updateDto
    //                 },
    //                 { new: true }
    //             );
    //             expect(result.data.contact).toBeDefined();
    //         });

    //         it("should handle update with null values", async () => {
    //             // Arrange
    //             const contactId = "contact1";
    //             const updateDto = {
    //                 email: null,
    //                 phone: null
    //             };
    //             const mockUpdatedContact = {
    //                 _id: contactId,
    //                 fullName: "John Doe",
    //                 email: null,
    //                 phone: null,
    //                 companyId: mockUser.companyId,
    //                 deleted: false
    //             };

    //             mockContactModel.findOneAndUpdate.mockResolvedValue(mockUpdatedContact);

    //             // Act
    //             const result = await service.update(contactId, updateDto as any, mockUser);

    //             // Assert
    //             expect(result.data.contact).toBeDefined();
    //             expect(result.data.contact.email).toBeNull();
    //             expect(result.data.contact.phone).toBeNull();
    //         });

    //         it("should handle update with undefined values", async () => {
    //             // Arrange
    //             const contactId = "contact1";
    //             const updateDto = {
    //                 email: undefined,
    //                 phone: undefined
    //             };
    //             const mockUpdatedContact = {
    //                 _id: contactId,
    //                 fullName: "John Doe",
    //                 companyId: mockUser.companyId,
    //                 deleted: false
    //             };

    //             mockContactModel.findOneAndUpdate.mockResolvedValue(mockUpdatedContact);

    //             // Act
    //             const result = await service.update(contactId, updateDto as any, mockUser);

    //             // Assert
    //             expect(result.data.contact).toBeDefined();
    //         });
    //     });
    // });

    // describe("remove", () => {
    //     const mockUser = { companyId: "company1", memberId: "member1" };

    //     beforeEach(() => {
    //         jest.clearAllMocks();
    //     });

    //     describe("successful removal", () => {
    //         it("should soft delete a contact", async () => {
    //             // Arrange
    //             const contactId = "contact1";
    //             const mockDeletedContact = {
    //                 _id: contactId,
    //                 fullName: "John Doe",
    //                 companyId: mockUser.companyId,
    //                 deleted: true
    //             };

    //             mockContactModel.findOneAndUpdate.mockResolvedValue(mockDeletedContact);

    //             // Act
    //             const result = await service.remove(contactId, mockUser);

    //             // Assert
    //             expect(mockContactModel.findOneAndUpdate).toHaveBeenCalledWith(
    //                 {
    //                     _id: contactId,
    //                     companyId: mockUser.companyId,
    //                     deleted: false
    //                 },
    //                 {
    //                     $set: { deleted: true }
    //                 },
    //                 { new: true }
    //             );
    //             expect(result).toHaveProperty("data");
    //             expect(result.data).toHaveProperty("message", "Contact deleted successfully");
    //         });

    //         it("should return success message for deleted contact", async () => {
    //             // Arrange
    //             const contactId = "contact1";
    //             const mockDeletedContact = {
    //                 _id: contactId,
    //                 fullName: "John Doe",
    //                 deleted: true
    //             };

    //             mockContactModel.findOneAndUpdate.mockResolvedValue(mockDeletedContact);

    //             // Act
    //             const result = await service.remove(contactId, mockUser);

    //             // Assert
    //             expect(result.data.message).toBe("Contact deleted successfully");
    //         });
    //     });

    //     describe("error handling", () => {
    //         it("should throw NotFoundException when contact is not found", async () => {
    //             // Arrange
    //             const contactId = "nonexistent";

    //             mockContactModel.findOneAndUpdate.mockResolvedValue(null);

    //             // Act & Assert
    //             await expect(service.remove(contactId, mockUser)).rejects.toThrow(NotFoundException);
    //         });

    //         it("should throw NotFoundException when contact is already deleted", async () => {
    //             // Arrange
    //             const contactId = "already-deleted";

    //             mockContactModel.findOneAndUpdate.mockResolvedValue(null);

    //             // Act & Assert
    //             await expect(service.remove(contactId, mockUser)).rejects.toThrow(NotFoundException);
    //         });

    //         it("should throw InternalServerErrorException when database operation fails", async () => {
    //             // Arrange
    //             const contactId = "contact1";
    //             const error = new Error("Database connection failed");

    //             mockContactModel.findOneAndUpdate.mockRejectedValue(error);

    //             // Act & Assert
    //             await expect(service.remove(contactId, mockUser)).rejects.toThrow(InternalServerErrorException);
    //         });

    //         it("should re-throw HttpException when it occurs", async () => {
    //             // Arrange
    //             const contactId = "contact1";
    //             const httpError = new BadRequestException("Invalid contact ID");

    //             mockContactModel.findOneAndUpdate.mockRejectedValue(httpError);

    //             // Act & Assert
    //             await expect(service.remove(contactId, mockUser)).rejects.toThrow(BadRequestException);
    //         });
    //     });

    //     describe("edge cases", () => {
    //         it("should handle removal of contact with complex data", async () => {
    //             // Arrange
    //             const contactId = "contact1";
    //             const mockDeletedContact = {
    //                 _id: contactId,
    //                 fullName: "Complex Contact",
    //                 email: "<EMAIL>",
    //                 phone: "1234567890",
    //                 linkedContacts: ["contact2", "contact3"],
    //                 comments: [{ body: "Test comment" }],
    //                 tags: ["tag1", "tag2"],
    //                 companyId: mockUser.companyId,
    //                 deleted: true
    //             };

    //             mockContactModel.findOneAndUpdate.mockResolvedValue(mockDeletedContact);

    //             // Act
    //             const result = await service.remove(contactId, mockUser);

    //             // Assert
    //             expect(result.data.message).toBe("Contact deleted successfully");
    //             expect(mockContactModel.findOneAndUpdate).toHaveBeenCalledWith(
    //                 {
    //                     _id: contactId,
    //                     companyId: mockUser.companyId,
    //                     deleted: false
    //                 },
    //                 {
    //                     $set: { deleted: true }
    //                 },
    //                 { new: true }
    //             );
    //         });

    //         it("should handle removal with different company IDs", async () => {
    //             // Arrange
    //             const contactId = "contact1";
    //             const differentUser = { companyId: "different-company", memberId: "member1" };
    //             const mockDeletedContact = {
    //                 _id: contactId,
    //                 fullName: "John Doe",
    //                 companyId: "different-company",
    //                 deleted: true
    //             };

    //             mockContactModel.findOneAndUpdate.mockResolvedValue(mockDeletedContact);

    //             // Act
    //             const result = await service.remove(contactId, differentUser);

    //             // Assert
    //             expect(mockContactModel.findOneAndUpdate).toHaveBeenCalledWith(
    //                 {
    //                     _id: contactId,
    //                     companyId: "different-company",
    //                     deleted: false
    //                 },
    //                 {
    //                     $set: { deleted: true }
    //                 },
    //                 { new: true }
    //             );
    //             expect(result.data.message).toBe("Contact deleted successfully");
    //         });
    //     });
    // });

    // describe("removeMultipleContacts", () => {
    //     it("should remove multiple contacts", async () => {
    //         mockContactModel.updateMany.mockResolvedValue({});
    //         const result = await service.removeMultipleContacts(["id1", "id2"], { companyId: "company1" });
    //         expect(result).toBeDefined();
    //     });
    // });

    // describe("permDelete", () => {
    //     it("should perm delete a contact", async () => {
    //         mockContactModel.findOneAndDelete.mockResolvedValue({});
    //         const result = await service.permDelete("id", { companyId: "company1" });
    //         expect(result).toBeDefined();
    //     });
    // });
    // describe("restore", () => {
    //     it("should restore a contact", async () => {
    //         mockContactModel.findOneAndUpdate.mockResolvedValue({});
    //         const result = await service.restore("id", { companyId: "company1" });
    //         expect(result).toBeDefined();
    //     });
    // });
    // describe("restoreMultipleContacts", () => {
    //     it("should restore multiple contacts", async () => {
    //         mockContactModel.updateMany.mockResolvedValue({});
    //         const result = await service.restoreMultipleContacts(["id1", "id2"], { companyId: "company1" });
    //         expect(result).toBeDefined();
    //     });
    // });
    // describe("addLinkedContact", () => {
    //     it("should add a linked contact", async () => {
    //         mockContactModel.updateOne.mockResolvedValue({});
    //         const result = await service.addLinkedContact(
    //             "id",
    //             [{ id: "id2", relationship: "relationship" }],
    //             { companyId: "company1" },
    //         );
    //         expect(result).toBeDefined();
    //     });
    // });
    // describe("getLinkedContact", () => {
    //     it("should get a linked contact", async () => {
    //         mockContactModel.aggregate.mockResolvedValue([{ data: [] }]);
    //         const result = await service.getLinkedContact("id", { companyId: "company1" });
    //         expect(result).toBeDefined();
    //     });
    // });
    // describe("removeLinkedContact", () => {
    //     it("should remove a linked contact", async () => {
    //         mockContactModel.updateOne.mockResolvedValue({});
    //         const result = await service.removeLinkedContact("id", "id2", { companyId: "company1" });
    //         expect(result).toBeDefined();
    //     });
    // });
    // describe("searchContacts", () => {
    //     it("should search contacts", async () => {
    //         mockContactModel.aggregate.mockResolvedValue([{ data: [] }]);
    //         const result = await service.searchContacts({} as any, { companyId: "company1" });
    //         expect(result).toBeDefined();
    //     });
    // });
    // describe("updateDnd", () => {
    //     it("should update dnd", async () => {
    //         mockContactModel.updateOne.mockResolvedValue({});
    //         const result = await service.updateDnd("id", { dnd: true } as any, { companyId: "company1" });
    //         expect(result).toBeDefined();
    //     });
    // });
    // describe("addTagsInMultipleContacts", () => {
    //     it("should add tags in multiple contacts", async () => {
    //         mockContactModel.updateMany.mockResolvedValue({});
    //         const result = await service.addTagsInMultipleContacts(["id1", "id2"], ["tag1", "tag2"], {
    //             companyId: "company1",
    //         });
    //         expect(result).toBeDefined();
    //     });
    // });
    // describe("removeTagsInMultipleContacts", () => {
    //     it("should remove tags in multiple contacts", async () => {
    //         mockContactModel.updateMany.mockResolvedValue({});
    //         const result = await service.removeTagsInMultipleContacts(["id1", "id2"], ["tag1", "tag2"], {
    //             companyId: "company1",
    //         });
    //         expect(result).toBeDefined();
    //     });
    // });
    // describe("getContactOpportunities", () => {
    //     it("should get contact opportunities", async () => {
    //         mockContactModel.aggregate.mockResolvedValue([{ data: [] }]);
    //         const result = await service.getContactOpportunities("id", { companyId: "company1" });
    //         expect(result).toBeDefined();
    //     });
    // });
    // describe("getContactNotes", () => {
    //     it("should get contact notes", async () => {
    //         // This method doesn't exist in the service, skipping test
    //         expect(true).toBe(true);
    //     });
    // });
    // describe("migrateContactAndOpp", () => {
    //     it("should migrate contact and opportunity", async () => {
    //         mockContactModel.updateOne.mockResolvedValue({});
    //         const result = await service.migrateContactAndOpp("id1", "id2", { mergeData: {} } as any, {
    //             companyId: "company1",
    //         });
    //         expect(result).toBeDefined();
    //     });
    // });
    // describe("getImportedContacts", () => {
    //     it("should get imported contacts", async () => {
    //         mockContactModel.aggregate.mockResolvedValue([{ data: [] }]);
    //         const result = await service.getImportedContacts("company1", { limit: 10, skip: 0 });
    //         expect(result).toBeDefined();
    //     });
    // });
    // describe("addComment", () => {
    //     it("should add comment", async () => {
    //         mockContactModel.updateOne.mockResolvedValue({});
    //         const result = await service.addComment("id", {
    //             body: "Test comment",
    //             currDate: new Date(),
    //             memberId: "user1"
    //         });
    //         expect(result).toBeDefined();
    //     });
    // });
    // describe("getComments", () => {
    //     it("should get comments", async () => {
    //         mockContactModel.aggregate.mockResolvedValue([{ data: [] }]);
    //         const result = await service.getComments("id");
    //         expect(result).toBeDefined();
    //     });
    // });
    // describe("updateComment", () => {
    //     it("should update a comment", async () => {
    //         mockContactModel.updateOne.mockResolvedValue({});
    //         const result = await service.updateComment({ memberId: "user1" }, "id", "commentId", "Updated comment");
    //         expect(result).toBeDefined();
    //     });
    // });
    // describe("deleteComment", () => {
    //     it("should delete a comment", async () => {
    //         mockContactModel.updateOne.mockResolvedValue({});
    //         const result = await service.deleteComment("id", "commentId", "user1");
    //         expect(result).toBeDefined();
    //     });
    // });
    // describe("createNewAction", () => {
    //     it("should create a new action", async () => {
    //         mockContactModel.updateOne.mockResolvedValue({});
    //         const result = await service.createNewAction("company1", "user1", "id", {
    //             id: "step1",
    //             type: "call" as any,
    //             body: "Test action",
    //             dueDate: new Date(),
    //             currDate: new Date()
    //         });
    //         expect(result).toBeDefined();
    //     });
    // });
    // describe("completeAction", () => {
    //     it("should complete an action", async () => {
    //         mockContactModel.updateOne.mockResolvedValue({});
    //         const result = await service.completeAction("company1", "user1", "id", {
    //             id: "step1",
    //             type: "call" as any,
    //             body: "Test action",
    //             dueDate: new Date(),
    //             currDate: new Date()
    //         });
    //         expect(result).toBeDefined();
    //     });
    // });
    // describe("deleteAction", () => {
    //     it("should delete an action", async () => {
    //         mockContactModel.updateOne.mockResolvedValue({});
    //         const result = await service.deleteAction("id", "actionId");
    //         expect(result).toBeDefined();
    //     });
    // });
    // describe("updateNextAction", () => {
    //     it("should update the next action", async () => {
    //         mockContactModel.updateOne.mockResolvedValue({});
    //         const result = await service.updateNextAction("id", { nextAction: "Follow up" });
    //         expect(result).toBeDefined();
    //     });
    // });
    // describe("getActions", () => {
    //     it("should get actions", async () => {
    //         mockContactModel.aggregate.mockResolvedValue([{ data: [] }]);
    //         const result = await service.getActions("id");
    //         expect(result).toBeDefined();
    //     });
    // });
    // describe("updateContactActivity", () => {
    //     it("should update contact activity", async () => {
    //         mockContactModel.updateOne.mockResolvedValue({});
    //         const result = await service.updateContactActivity("company1", "user1", {
    //             id: "id",
    //             body: "test activity",
    //             currDate: new Date()
    //         });
    //         expect(result).toBeDefined();
    //     });
    // });
    // describe("getContactActivity", () => {
    //     it("should get contact activity", async () => {
    //         mockContactModel.aggregate.mockResolvedValue([{ data: [] }]);
    //         const result = await service.getContactActivity("id");
    //         expect(result).toBeDefined();
    //     });
    // });
    // describe("getContactOfTypeLeads", () => {
    //     it("should get contact of type leads", async () => {
    //         mockContactModel.aggregate.mockResolvedValue([{ data: [] }]);
    //         const result = await service.getContactOfTypeLeads("active", false, { companyId: "company1" });
    //         expect(result).toBeDefined();
    //     });
    // });
    // describe("getLeadByContactId", () => {
    //     it("should get lead by contact id", async () => {
    //         mockContactModel.aggregate.mockResolvedValue([{ data: [] }]);
    //         const result = await service.getLeadByContactId("id", "company1");
    //         expect(result).toBeDefined();
    //     });
    // });

    // describe("migrateTrackingAttribution", () => {
    //     it("should migrate tracking attribution", async () => {
    //         mockContactModel.updateOne.mockResolvedValue({});
    //         const result = await service.migrateTrackingAttribution();
    //         expect(result).toBeDefined();
    //     });
    // });
    // describe("migrateBusinessName", () => {
    //     it("should migrate business name", async () => {
    //         mockContactModel.updateOne.mockResolvedValue({});
    //         const result = await service.migrateBusinessName();
    //         expect(result).toBeDefined();
    //     });
    // });
    // describe("migrateLeadToContact2", () => {
    //     it("should migrate lead to contact", async () => {
    //         mockContactModel.updateOne.mockResolvedValue({});
    //         const result = await service.migrateLeadToContact2();
    //         expect(result).toBeDefined();
    //     });
    // });
});
