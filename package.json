{"name": "nhr", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:local": "cross-env STAGE=local nest start --watch", "start:dev": "cross-env STAGE=dev nest start --watch", "start:debug": "nest start --debug --watch", "start:server": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json"}, "dependencies": {"@aws-sdk/client-s3": "^3.657.0", "@aws-sdk/s3-request-presigner": "^3.657.0", "@casl/ability": "^6.3.2", "@hapi/joi": "^17.1.1", "@nestjs-modules/mailer": "^1.8.1", "@nestjs/cli": "^9.0.0", "@nestjs/common": "^9.0.0", "@nestjs/config": "^2.2.0", "@nestjs/core": "^9.0.0", "@nestjs/jwt": "^9.0.0", "@nestjs/mongoose": "^9.2.1", "@nestjs/passport": "^9.0.0", "@nestjs/platform-express": "^9.0.0", "@nestjs/schedule": "^4.1.0", "@nestjs/swagger": "^6.1.3", "@types/nodemailer": "^6.4.6", "@types/passport-jwt": "^3.0.7", "argon2": "^0.30.2", "class-transformer": "^0.5.1", "class-validator": "^0.13.2", "compression": "^1.7.4", "cookie-parser": "^1.4.6", "cross-env": "^7.0.3", "crypto": "^1.0.1", "csurf": "^1.11.0", "date-fns": "^2.29.3", "dotenv": "^16.0.3", "handlebars": "^4.7.7", "helmet": "^6.0.0", "hpp": "^0.2.3", "joi": "^17.7.0", "jsonwebtoken": "^8.5.1", "mongoose": "^6.7.0", "nodemailer": "^6.8.0", "passport": "^0.6.0", "passport-jwt": "^4.0.0", "reflect-metadata": "^0.1.13", "rimraf": "^3.0.2", "rxjs": "^7.2.0", "stripe": "^16.5.0"}, "devDependencies": {"@nestjs/schematics": "^9.0.0", "@nestjs/testing": "^9.0.0", "@types/cookie-parser": "^1.4.3", "@types/express": "^4.17.13", "@types/jest": "28.1.8", "@types/node": "^16.0.0", "@types/supertest": "^2.0.11", "@typescript-eslint/eslint-plugin": "^5.42.1", "@typescript-eslint/parser": "^5.42.1", "eslint": "^8.27.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.0.0", "jest": "28.1.3", "prettier": "^2.7.1", "source-map-support": "^0.5.20", "supertest": "^6.1.3", "ts-jest": "28.0.8", "ts-loader": "^9.2.3", "ts-node": "^10.0.0", "tsconfig-paths": "4.1.0", "typescript": "^4.7.4"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node", "moduleNameMapper": {"^src/(.*)$": "<rootDir>/$1"}}}