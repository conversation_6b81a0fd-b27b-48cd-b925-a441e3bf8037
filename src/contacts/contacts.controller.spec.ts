import { Test, TestingModule } from "@nestjs/testing";
import { ContactsController } from "./contacts.controller";
import { ContactsService } from "./contacts.service";
import { JwtService } from "@nestjs/jwt";
import { CompanyService } from "src/company/company.service";
import { PositionService } from "src/position/position.service";
import { RoleService } from "src/role/role.service";

describe("ContactsController", () => {
    let controller: ContactsController;
    let service: ContactsService;

    const mockService = {
        create: jest.fn(),
        findAll: jest.fn(),
        findOne: jest.fn(),
        update: jest.fn(),
        remove: jest.fn(),
        removeMultipleContacts: jest.fn(),
        permDelete: jest.fn(),
        restore: jest.fn(),
        restoreMultipleContacts: jest.fn(),
        addLinkedContact: jest.fn(),
        removeLinkedContact: jest.fn(),
        searchContacts: jest.fn(),
        getLinkedContact: jest.fn(),
        updateDnd: jest.fn(),
        addTagsInMultipleContacts: jest.fn(),
        removeTagsInMultipleContacts: jest.fn(),
        getContactOpportunities: jest.fn(),
        migrateContactAndOpp: jest.fn(),
        getImportedContacts: jest.fn(),
        addComment: jest.fn(),
        getComments: jest.fn(),
        updateComment: jest.fn(),
        deleteComment: jest.fn(),
        updateLeadStatus: jest.fn(),
        createNewAction: jest.fn(),
        completeAction: jest.fn(),
        getActions: jest.fn(),
        getContactActivity: jest.fn(),
        updateContactActivity: jest.fn(),
        getLeadByContactId: jest.fn(),
    };

    beforeEach(async () => {
        jest.clearAllMocks();
        const module: TestingModule = await Test.createTestingModule({
            controllers: [ContactsController],
            providers: [
                { provide: ContactsService, useValue: mockService },
                { provide: JwtService, useValue: { sign: jest.fn(), verify: jest.fn() } },
                { provide: CompanyService, useValue: { getCompanyById: jest.fn() } },
                { provide: PositionService, useValue: { checkPermissionNew: jest.fn() } },
                { provide: RoleService, useValue: { checkRole: jest.fn() } },
            ],
        }).compile();

        controller = module.get<ContactsController>(ContactsController);
        service = module.get<ContactsService>(ContactsService);
    });

    describe("create", () => {
        it("should call service.create and return result", async () => {
            const mockResult = { id: "1" };
            mockService.create.mockResolvedValue(mockResult);
            const result = await controller.create({} as any, {} as any);
            expect(service.create).toHaveBeenCalled();
            expect(result).toBe(mockResult);
        });
        it("should handle errors from service.create", async () => {
            mockService.create.mockRejectedValue(new Error("fail"));
            await expect(controller.create({} as any, {} as any)).rejects.toThrow("fail");
        });
    });

    describe("findAll", () => {
        it("should call service.findAll and return result", async () => {
            const mockResult = [{ id: "1" }];
            mockService.findAll.mockResolvedValue(mockResult);
            const result = await controller.findAll({} as any, {} as any);
            expect(service.findAll).toHaveBeenCalled();
            expect(result).toBe(mockResult);
        });
        it("should handle errors from service.findAll", async () => {
            mockService.findAll.mockRejectedValue(new Error("fail"));
            await expect(controller.findAll({} as any, {} as any)).rejects.toThrow("fail");
        });
    });

    describe("findOne", () => {
        it("should call service.findOne and return result", async () => {
            const mockResult = { id: "1" };
            mockService.findOne.mockResolvedValue(mockResult);
            const result = await controller.findOne("id", {} as any);
            expect(service.findOne).toHaveBeenCalledWith("id", expect.anything());
            expect(result).toBe(mockResult);
        });
        it("should handle errors from service.findOne", async () => {
            mockService.findOne.mockRejectedValue(new Error("fail"));
            await expect(controller.findOne("id", {} as any)).rejects.toThrow("fail");
        });
    });

    describe("update", () => {
        it("should call service.update and return result", async () => {
            const mockResult = { id: "1" };
            mockService.update.mockResolvedValue(mockResult);
            const result = await controller.update("id", {} as any, {} as any);
            expect(service.update).toHaveBeenCalled();
            expect(result).toBe(mockResult);
        });
        it("should handle errors from service.update", async () => {
            mockService.update.mockRejectedValue(new Error("fail"));
            await expect(controller.update("id", {} as any, {} as any)).rejects.toThrow("fail");
        });
    });

});
